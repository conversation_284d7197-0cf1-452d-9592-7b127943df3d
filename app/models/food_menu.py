from tortoise import fields
from enum import IntEnum
from .base import BaseModel, TimestampMixin

class MealType(IntEnum):
    BREAKFAST = 1
    MORNING_SNACK = 2
    LUNCH = 3
    AFTERNOON_SNACK = 4
    DINNER = 5


class FoodMenuOneStudent(BaseModel, TimestampMixin):
    id = fields.IntField(pk=True, description="食谱菜单id")
    food_menu_name = fields.CharField(max_length=20, description="食谱菜单名称")
    menu_date = fields.DateField(description="菜单日期", index=True)
    meal_type = fields.IntEnumField(MealType, description="餐次类型")
    dish_list = fields.JSONField(description="菜品列表")#[{"dish_id": "1", "dish_name": "红烧肉", "ingredients": [{"food_stuff_id": 1, "food_stuff_name": "猪肉", "unit_name": "克", "quantity": 100}]}]
    is_active = fields.BooleanField(default=True, description="是否启用")
    school_id = fields.IntField(description="学校ID", index=True)

    class Meta:
        table = "food_menu_one_student"
        table_description = "菜单配料"
