from fastapi import APIRouter, Query, Body

from app.controllers.food_menu import food_menu_controller
from app.schemas import Success, SuccessExtra
from app.schemas.food_menu import FoodMenuOneStudentCreate, FoodMenuOneStudentUpdate, FoodMenuOneStudentSearch

router = APIRouter()


@router.get("/list", summary="查询食谱菜单列表")
async def list_food_menu(
    food_menu_name: str = Query(None, description="菜单名称"),
    meal_type: int = Query(None, description="餐次类型"),
    menu_date: str = Query(None, description="菜单日期"),
    page: int = Query(1, description="当前页码"),
    page_size: int = Query(10, description="每页数量"),
):
    # 处理日期参数
    from datetime import datetime
    parsed_menu_date = None
    if menu_date:
        try:
            parsed_menu_date = datetime.strptime(menu_date, "%Y-%m-%d").date()
        except ValueError:
            parsed_menu_date = None

    search = FoodMenuOneStudentSearch(
        foodMenuName=food_menu_name,
        mealType=meal_type,
        menuDate=parsed_menu_date,
        current=page,
        size=page_size
    )
    result = await food_menu_controller.get_food_menu_list(search)
    return SuccessExtra(
        data=result["data"],
        total=result["total"],
        page=page,
        page_size=page_size
    )


@router.get("/get", summary="获取食谱菜单详情")
async def get_food_menu(
    id: int = Query(..., description="食谱菜单ID"),
):
    food_menu = await food_menu_controller.get_food_menu_by_id(id)
    data = await food_menu.to_dict()
    return Success(data=data)


@router.post("/create", summary="创建食谱菜单")
async def create_food_menu(
    food_menu_in: FoodMenuOneStudentCreate,
):
    await food_menu_controller.create_food_menu(obj_in=food_menu_in)
    return Success(msg="创建成功")


@router.post("/update", summary="更新食谱菜单")
async def update_food_menu(
    id: int = Query(..., description="食谱菜单ID"),
    food_menu_in: FoodMenuOneStudentUpdate = Body(...),
):
    await food_menu_controller.update_food_menu(id=id, obj_in=food_menu_in)
    return Success(msg="更新成功")


@router.delete("/delete", summary="删除食谱菜单")
async def delete_food_menu(
    id: int = Query(..., description="食谱菜单ID"),
):
    await food_menu_controller.delete_food_menu(id=id)
    return Success(msg="删除成功")